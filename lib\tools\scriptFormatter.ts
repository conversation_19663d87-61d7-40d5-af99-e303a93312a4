/**
 * Script Formatter Tool
 *
 * This tool formats scripts from the byteStore collection to make them easier to rehearse with
 * and adds additional information to the UI.
 */

import { z } from 'zod';
import { llmTool, LlmProvider } from './llm-tool';

// Define the schema for script metadata
const ScriptMetadataSchema = z.object({
  title: z.string().describe("The title of the script"),
  author: z.string().describe("The author or playwright of the script"),
  characters: z.array(z.string()).describe("List of character names in the script"),
  summary: z.string().describe("A brief summary of the script and its setting"),
});

// Define the schema for formatted script lines
const ScriptLineSchema = z.object({
  lineNumber: z.number().describe("The line number in the script"),
  character: z.string().describe("The character speaking this line"),
  text: z.string().describe("The text/dialogue of this line"),
  notes: z.string().optional().describe("Optional stage directions or notes about this line"),
});

// Define the schema for the formatted script
const FormattedScriptSchema = z.object({
  metadata: ScriptMetadataSchema,
  lines: z.array(ScriptLineSchema),
});

// Define types based on the schemas
export type ScriptMetadata = z.infer<typeof ScriptMetadataSchema>;
export type ScriptLine = z.infer<typeof ScriptLineSchema>;
export type FormattedScript = z.infer<typeof FormattedScriptSchema>;

export interface ScriptFormattingOptions {
  model?: string;
  provider?: LlmProvider;
  includeLineNumbers?: boolean;
  includeNotes?: boolean;
}

export class ScriptFormatterTool {
  // Static description for AI agents
  static description = {
    name: "scriptFormatter",
    description: "Formats scripts to make them easier to rehearse with and adds additional information",
    schema: {
      formatScript: {
        description: "Format a script for rehearsal",
        parameters: {
          scriptContent: "The raw script content to format",
          options: "Optional formatting options"
        },
        returns: "A formatted script with metadata and structured lines"
      }
    }
  };

  /**
   * Format a script for rehearsal
   * @param scriptContent - The raw script content
   * @param options - Formatting options
   * @returns Formatted script with metadata and structured lines
   */
  async formatScript(
    scriptContent: string,
    options: ScriptFormattingOptions = {}
  ): Promise<FormattedScript> {
    // Check if this is likely a PDF or other non-text format
    const isProbablyPdf = this._isProbablyPdfContent(scriptContent);
    try {
      // If content appears to be PDF, use a special prompt
      if (isProbablyPdf) {
        console.log("Detected PDF-like content, using special handling...");
        return this._handlePdfContent(scriptContent, options);
      }
      const {
        model = "gemini-2.5-pro-preview-05-06",
        provider = "google",
        includeLineNumbers = true,
        includeNotes = true
      } = options;

      // Create a system prompt for the LLM with enhanced Zod schema
      const systemPrompt = this._createSystemPrompt(includeLineNumbers, includeNotes);

      // Process the script with the LLM
      let llmResponse;
      try {
        llmResponse = await llmTool.processContent({
          prompt: scriptContent,
          context: systemPrompt,
          model,
          provider,
          modelOptions: {
            temperature: 0.2,
            maxTokens: 7000
          }
        });

        // Check if the response contains an error message
        if (llmResponse.includes("Error from") || llmResponse.includes("Failed to")) {
          console.warn("LLM returned an error:", llmResponse);
          throw new Error("LLM processing error: " + llmResponse.substring(0, 100) + "...");
        }
      } catch (llmError) {
        console.error("Error during LLM processing:", llmError);
        throw new Error("Failed to process script with LLM: " +
          (llmError instanceof Error ? llmError.message : String(llmError)));
      }

      // Parse the LLM response to extract the formatted script
      const formattedScript = this._parseLlmResponse(llmResponse, scriptContent);

      // Return the formatted script
      return formattedScript;
    } catch (error: any) {
      console.error("Error formatting script:", error);
      throw new Error("Script formatting failed: " + error.message);
    }
  }

  /**
   * Create a system prompt for the LLM with enhanced Zod schema
   * @param includeLineNumbers - Whether to include line numbers
   * @param includeNotes - Whether to include notes/stage directions
   * @returns System prompt for the LLM
   */
  private _createSystemPrompt(includeLineNumbers: boolean, includeNotes: boolean): string {
    // Generate schema description for LLM
    const schemaDescription = this._generateSchemaDescription();

    return "You are a professional script formatter specialized in preparing scripts for actors to rehearse with.\n" +
      "Your task is to analyze the provided script and extract key information to make it easier to rehearse.\n\n" +
      "Your output MUST strictly conform to this JSON schema:\n" +
      schemaDescription + "\n\n" +
      "First, extract the following metadata:\n" +
      "1. Title - The title of the script or play\n" +
      "2. Author - The author or playwright\n" +
      "3. Characters - A list of all character names that appear in the script\n" +
      "4. Summary - A brief summary of the script and its setting (2-3 sentences)\n\n" +
      "Then, format the script into a structured format with the following for each line:\n" +
      "1. Line Number - The sequential number of the line in the script\n" +
      "2. Character - The character speaking this line\n" +
      "3. Text - The actual dialogue text\n" +
      "4. Notes - Any stage directions or notes about how the line should be delivered (if present)\n\n" +
      (includeLineNumbers ? "Include line numbers for each line of dialogue." : "Do not include line numbers.") + "\n" +
      (includeNotes ? "Include any stage directions or notes about how lines should be delivered." : "Do not include stage directions or notes.") + "\n\n" +
      "Make sure to properly identify character names and separate them from dialogue.\n" +
      "If you're unsure about a character name, make your best guess based on the context.\n" +
      "If there are stage directions or scene descriptions, include them as notes for the relevant lines.\n\n" +
      "IMPORTANT: Your response must be valid JSON that exactly matches the schema. Do not include any text outside of the JSON object.";
  }

  /**
   * Generate a user-friendly schema description from the Zod schema
   * @returns A string representation of the schema suitable for LLM instruction
   */
  private _generateSchemaDescription(): string {
    // Convert Zod schema to a format the LLM can understand
    const schemaObj = {
      metadata: {
        title: "string - The title of the script",
        author: "string - The author or playwright",
        characters: ["string - Character names in the script"],
        summary: "string - A brief summary of the script"
      },
      lines: [
        {
          lineNumber: "number - The line number in the script",
          character: "string - The character speaking this line",
          text: "string - The text/dialogue of this line",
          notes: "string (optional) - Stage directions or notes"
        }
      ]
    };

    return JSON.stringify(schemaObj, null, 2);
  }

  /**
   * Parse the LLM response to extract the formatted script with enhanced validation
   * @param llmResponse - The response from the LLM
   * @returns Formatted script object
   */
  private _parseLlmResponse(llmResponse: string, originalContent: string = ""): FormattedScript {
    try {
      // Find JSON content in the response
      const jsonMatch = llmResponse.match(/```json\n([\s\S]*?)\n```/) || llmResponse.match(/{[\s\S]*}/);

      const jsonContent = jsonMatch ? (jsonMatch[1] ? jsonMatch[1] : jsonMatch[0]) : llmResponse;

      try {
        // Parse the JSON content
        const parsedResponse = JSON.parse(jsonContent);

        // Validate the response against the schema
        const validationResult = FormattedScriptSchema.safeParse(parsedResponse);

        if (!validationResult.success) {
          // Enhanced error handling with Zod validation errors
          const errorDetails = validationResult.error.errors.map(err =>
            `${err.path.join('.')}: ${err.message}`
          ).join(', ');

          console.error("Schema validation errors:", errorDetails);
          throw new Error("Schema validation failed: " + errorDetails);
        }

        return validationResult.data;
      } catch (parseError) {
        // If JSON parsing fails, try to create a basic structure
        console.error("JSON parsing failed, attempting to create fallback structure", parseError);

        // Create a fallback script structure with more helpful information
        const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
        console.warn("Creating fallback script due to error:", errorMessage);

        // Try to extract some basic information from the script content
        const scriptLines = llmResponse.split('\n').filter((line: string) => line.trim().length > 0);

        // Use the filename as the title if available, otherwise use first line
        const possibleTitle = originalContent.split('\n')[0]?.trim() || "Script";

        // Try to identify potential character names in the script
        const potentialCharacters = new Set<string>();
        const characterRegex = /^([A-Z][A-Za-z]*(?:\s[A-Z][A-Za-z]*)?):?\s/;

        originalContent.split('\n').forEach((line: string) => {
          const match = line.match(characterRegex);
          if (match && match[1] && match[1].length > 1) {
            potentialCharacters.add(match[1].trim());
          }
        });

        const characters = Array.from(potentialCharacters).slice(0, 10); // Limit to 10 characters

        const fallbackScript: FormattedScript = {
          metadata: {
            title: possibleTitle,
            author: "", // Leave blank to avoid showing "Unknown Author"
            characters: characters.length > 0 ? characters : [],
            summary: characters.length > 0
              ? "Extracted character names from script content."
              : "Could not extract information from script."
          },
          lines: []
        };

        // Add some sample lines from the original script if available
        if (scriptLines.length > 0) {
          // First add a note about the parsing error
          // Don't add an error message line, just start with the content

          // Then add content from the script
          let lineNumber = 1; // Start line numbering at 1

          for (let i = 0; i < Math.min(30, scriptLines.length); i++) {
            if (scriptLines[i].trim().length > 0) {
              // Try to identify if this line has a character name
              const match = scriptLines[i].match(characterRegex);
              let character = "";
              let text = scriptLines[i].trim();

              if (match && match[1]) {
                character = match[1].trim();
                text = scriptLines[i].substring(match[0].length).trim();
              } else if (i > 0 && scriptLines[i-1].trim().length === 0 && text.toUpperCase() === text && text.length < 30) {
                // This might be a character name on its own line
                character = text;
                text = "";
              } else {
                character = "";
              }

              fallbackScript.lines.push({
                lineNumber: lineNumber++,
                character: character || "Content",
                text: text,
                notes: ""
              });
            }
          }

          if (scriptLines.length > 30) {
            fallbackScript.lines.push({
              lineNumber: lineNumber,
              character: "Note",
              text: "... more content available in the original script ...",
              notes: ""
            });
          }
        }

        return fallbackScript;
      }
    } catch (error: any) {
      console.error("Error parsing LLM response:", error);
      throw new Error("Failed to parse script format: " + error.message);
    }
  }

  /**
   * Handle PDF or other complex content formats with enhanced Zod schema
   * @param content - The PDF or complex content
   * @param options - Formatting options
   * @returns Formatted script with best-effort extraction
   */
  private async _handlePdfContent(content: string, options: ScriptFormattingOptions): Promise<FormattedScript> {
    const { model = "gemini-2.5-pro-preview-05-06", provider = "google" } = options;

    // Create a special prompt for PDF content with schema
    const schemaDescription = this._generateSchemaDescription();
    const pdfPrompt = `
    You are a script extraction specialist. The following content appears to be from a PDF or complex format.
    Please do your best to extract a theatrical script from this content, focusing on:

    1. Identifying the title and author
    2. Finding character names
    3. Extracting dialogue and stage directions
    4. Providing a brief summary of the plot

    Your output MUST strictly conform to this JSON schema:
    ${schemaDescription}

    Even if the content is partially corrupted, extract whatever meaningful text you can find.

    IMPORTANT: Your response must be valid JSON that exactly matches the schema. Do not include any text outside of the JSON object.
    `;

    try {
      // Process with the LLM using a higher temperature for more creative extraction
      const llmResponse = await llmTool.processContent({
        prompt: content.substring(0, 15000), // Limit to first 15K chars to avoid token limits
        context: pdfPrompt,
        model,
        provider,
        modelOptions: {
          temperature: 0.7, // Higher temperature for more creative extraction
          maxTokens: 7000
        }
      });

      // Parse the response
      return this._parseLlmResponse(llmResponse, content);
    } catch (error) {
      console.error("Error processing PDF content:", error);

      // Create a basic fallback structure
      // Try to extract some basic information from the content
      const contentLines = content.split('\n').filter((line: string) => line.trim().length > 0);
      const potentialCharacters = new Set<string>();
      const characterRegex = /^([A-Z][A-Za-z]*(?:\s[A-Z][A-Za-z]*)?):?\s/;

      contentLines.slice(0, 100).forEach((line: string) => {
        const match = line.match(characterRegex);
        if (match && match[1] && match[1].length > 1) {
          potentialCharacters.add(match[1].trim());
        }
      });

      const characters = Array.from(potentialCharacters).slice(0, 10);

      return {
        metadata: {
          title: "PDF Document",
          author: "", // Leave blank to avoid showing "Unknown Author"
          characters: characters.length > 0 ? characters : [],
          summary: "This appears to be a PDF document. Showing extracted content below."
        },
        lines: [
          // Start with content directly, no need for a note
          ...(() => {
            const lines: ScriptLine[] = [];
            let lineNumber = 1;

            // Process up to 30 lines
            contentLines.slice(0, 30).forEach(line => {
              if (line.trim().length > 0) {
                // Try to identify if this line has a character name
                const match = line.match(characterRegex);
                let character = "";
                let text = line.trim();

                if (match && match[1]) {
                  character = match[1].trim();
                  text = line.substring(match[0].length).trim();
                } else {
                  character = "Content";
                }

                lines.push({
                  lineNumber: lineNumber++,
                  character,
                  text,
                  notes: ""
                });
              }
            });

            // Add a note if there's more content
            if (contentLines.length > 30) {
              lines.push({
                lineNumber: lineNumber,
                character: "Note",
                text: "... more content available in the original PDF ...",
                notes: ""
              });
            }

            return lines;
          })()
        ].filter(line => line !== null) as ScriptLine[]
      };
    }
  }

  /**
   * Check if content appears to be PDF or other non-text format
   * @param content - The content to check
   * @returns True if content appears to be PDF or binary
   */
  private _isProbablyPdfContent(content: string): boolean {
    // Check for PDF header
    if (content.startsWith('%PDF')) {
      return true;
    }

    // Check for high concentration of non-printable characters
    const nonPrintableCount = (content.match(/[^\x20-\x7E\t\n\r]/g) || []).length;
    const ratio = nonPrintableCount / content.length;

    // If more than 15% of characters are non-printable, it's likely binary
    return ratio > 0.15;
  }

  /**
   * Get the tool description for use by AI agents
   * @returns Tool description in a standardized format
   */
  getDescription(): typeof ScriptFormatterTool.description {
    return ScriptFormatterTool.description;
  }

  /**
   * Get all available tool methods with their descriptions
   * @returns Map of method names to their descriptions
   */
  getAvailableMethods(): Record<string, string> {
    return {
      formatScript: "Format a script for rehearsal"
    };
  }
}

// Export a singleton instance
export const scriptFormatterTool = new ScriptFormatterTool();