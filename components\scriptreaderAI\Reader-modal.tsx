"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, MessageSquare, FileText, Mic, Loader, Info, Book } from "lucide-react"
import ChatTab from "./ChatTab"
import { getFirestore, collection, query, where, getDocs, addDoc, serverTimestamp, orderBy, limit, doc, getDoc } from "firebase/firestore"
import { useGetNamespace } from "./useGetNamespace"
import { useSession } from "next-auth/react"
import { v4 as uuidv4 } from "uuid"
import useUpload, { StatusText } from "./useUpload"
import { useConversation } from "@11labs/react"
import { db } from "components/firebase"
import SideBar from "./SideBar"
import Rehearsals from "./Rehearsals"
import FileDetails from "./FileDetails"
import ScriptTab from "./ScriptTab"

interface ScriptFile {
  id: string
  name: string
  namespace: string
}

interface ChatMessage {
  id?: string
  tempId?: string
  role: "user" | "assistant"
  content: string
  timestamp: string
  audioUrl?: string
  fileDocumentId?: string
}

interface ReadermodalProps {
  isOpen: boolean
  onClose: () => void
  fileId?: string
}

function Readermodal({ isOpen, onClose, fileId }: ReadermodalProps) {
  const [activeTab, setActiveTab] = useState<string | null>(null)
  const [activeSection, setActiveSection] = useState<string>("rehearsing")
  const [scriptFiles, setScriptFiles] = useState<ScriptFile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [uploadProgress, setUploadProgress] = useState<number | null>(null)
  const [uploadStatusText, setUploadStatusText] = useState<string>("Uploading script...")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [chatId, setChatId] = useState<string | null>(null)
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [fileDocumentId, setFileDocumentId] = useState<string | null>(null)
  const [selectedFileNamespace, setSelectedFileNamespace] = useState<string | null>(null)
  const [fileName, setFileName] = useState<string | null>(null)
  const isMounted = useRef(true)
  const messagesProcessingRef = useRef(false)
  const [hasPermission, setHasPermission] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [voiceErrorMessage, setVoiceErrorMessage] = useState("")
  const [apiConfigStatus, setApiConfigStatus] = useState<'unchecked' | 'valid' | 'invalid' | 'connecting'>('unchecked')
  const [detailedErrorInfo, setDetailedErrorInfo] = useState<string | null>(null)
  const { data: session, status: sessionStatus } = useSession()
  const userId = session?.user?.email || ""
  const { handleUpload, progress, status, error: uploadError } = useUpload()
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)

  // Script-related state variables
  const [scriptContent, setScriptContent] = useState<string>("")
  const [isScriptLoading, setIsScriptLoading] = useState<boolean>(false)
  const [isScriptReady, setIsScriptReady] = useState<boolean>(false)
  const [isFormatting, setIsFormatting] = useState<boolean>(false)
  const [formattedMarkdown, setFormattedMarkdown] = useState<string>("")

  const conversation = useConversation({
    apiKey: process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || '***************************************************',
    onConnect: () => {
      console.log("Connected to ElevenLabs API successfully")
      setApiConfigStatus('valid')
      setDetailedErrorInfo(null)
    },
    onDisconnect: () => {
      console.log("Disconnected from ElevenLabs")
      setIsListening(false)
      if (apiConfigStatus === 'valid') {
        setApiConfigStatus('unchecked')
      }
    },
    onError: (error: unknown) => {
      console.error("ElevenLabs API error:", error)
      const errorMessage = error instanceof Error ? error.message : String(error)
      setDetailedErrorInfo(errorMessage)
      setApiConfigStatus('invalid')
      if (isListening) {
        setIsListening(false)
        setVoiceErrorMessage("Connection error: " + errorMessage.substring(0, 100))
      }
    },
    onMessage: (message) => {
      if (typeof message === "string") {
        console.log("Voice input:", message)
      } else if (message && typeof message === "object" && 'message' in message) {
        console.log("AI response:", message.message)
      }
    },
  })

  const { status: voiceStatus, isSpeaking } = conversation

  useEffect(() => {
    console.log("ElevenLabs config check:", {
      agentIdAvailable: !!process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID,
      apiKeyAvailable: !!process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY,
    })
    if (isOpen) {
      setApiConfigStatus('unchecked')
    }
  }, [isOpen])

  useEffect(() => {
    if (fileId) {
      setActiveTab(fileId)
    }
  }, [fileId])

  const { namespace, fileName: namespaceFileName } = useGetNamespace(userId, activeTab || null)

  useEffect(() => {
    if (namespaceFileName) {
      setFileName(namespaceFileName)
    }
  }, [namespaceFileName])

  useEffect(() => {
    if (status === StatusText.ERROR && uploadError) {
      setError(`Upload error: ${uploadError}`)
      setIsUploading(false)
      setUploadProgress(null)
    } else if (status === StatusText.UPLOADING) {
      setIsUploading(true)
      setUploadProgress(progress || 0)
      setUploadStatusText("Uploading script...")
    } else if (status === StatusText.PROCESSING) {
      setIsUploading(true)
      setUploadProgress(progress || 0)
      setUploadStatusText("Processing script...")
    } else if (status === StatusText.COMPLETED) {
      setIsUploading(false)
      setUploadProgress(null)
      fetchScriptFiles()
    }
  }, [status, progress, uploadError])

  useEffect(() => {
    const requestMicPermission = async () => {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true })
        setHasPermission(true)
        setVoiceErrorMessage("")
      } catch (error) {
        setVoiceErrorMessage("Microphone access denied - please enable in browser settings")
        console.error("Error accessing microphone:", error)
      }
    }

    if (activeSection === "rehearsing") {
      requestMicPermission()
    }
  }, [activeSection])

  const handleStartConversation = async () => {
    try {
      setVoiceErrorMessage("")
      setDetailedErrorInfo(null)
      if (!session?.user?.email) {
        throw new Error("You must be signed in to use voice features")
      }
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_SCRIPT_AGENT_ID || '1WU4LPk9482VXQFb80aq'
      if (!agentId) {
        throw new Error("Missing ElevenLabs agent ID configuration")
      }
      setApiConfigStatus('connecting')
      setIsListening(true)
      console.log("Attempting to start conversation with agent ID:", agentId)
      const requestParams: any = {}
      if (activeTab) {
        requestParams.scriptId = activeTab
        requestParams.scriptName = fileName || "Unknown script"
      }
      const conversationId = await conversation.startSession({
        agentId: agentId,
        ...requestParams
      })
      console.log("ElevenLabs conversation started successfully:", conversationId)
      setApiConfigStatus('valid')
    } catch (error) {
      console.error("Error starting conversation:", error)
      let errorMessage = "Failed to start conversation"
      let detailedMessage = error instanceof Error ? error.message : String(error)
      if (detailedMessage.includes("agentId")) {
        errorMessage = "Invalid agent ID configuration"
      } else if (detailedMessage.includes("auth") || detailedMessage.includes("API key") || detailedMessage.includes("key")) {
        errorMessage = "API authentication failed - missing or invalid API key"
      } else if (detailedMessage.includes("network") || detailedMessage.includes("connect")) {
        errorMessage = "Network connection failed - please check your internet connection"
      } else if (detailedMessage.includes("sign in") || detailedMessage.includes("auth")) {
        errorMessage = "Authentication required - please sign in"
      }
      setVoiceErrorMessage(errorMessage)
      setDetailedErrorInfo(detailedMessage)
      setIsListening(false)
      setApiConfigStatus('invalid')
    }
  }

  const handleEndConversation = async () => {
    try {
      setIsListening(false)
      await conversation.endSession()
    } catch (error) {
      console.error("Error ending conversation:", error)
      setVoiceErrorMessage("Failed to end conversation")
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
    }
  }

  const toggleMute = async () => {
    try {
      await conversation.setVolume({ volume: isMuted ? 1 : 0 })
      setIsMuted(!isMuted)
    } catch (error) {
      console.error("Error changing volume:", error)
      setVoiceErrorMessage("Failed to change volume")
      setDetailedErrorInfo(error instanceof Error ? error.message : String(error))
    }
  }

  const fetchMostRecentFile = async (): Promise<{id: string, namespace: string, name: string} | null> => {
    if (!userId) return null
    try {
      const filesRef = collection(db, `users/${userId}/files`)
      const q = query(filesRef, orderBy("createdAt", "desc"), limit(1))
      const querySnapshot = await getDocs(q)
      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0]
        const fileData = fileDoc.data()
        return {
          id: fileDoc.id,
          namespace: fileData.namespace || fileDoc.id,
          name: fileData.name || "Untitled Document"
        }
      }
      return null
    } catch (err) {
      console.error("Error fetching most recent file:", err)
      return null
    }
  }

  const fetchScriptFiles = async () => {
    if (sessionStatus === "loading") {
      return
    }
    if (!session?.user?.email) {
      setError("Please sign in to access your scripts.")
      setLoading(false)
      return
    }

    try {
      const db = getFirestore()
      const filesRef = collection(db, `users/${session.user.email}/files`)
      const q = query(
        filesRef,
        where("category", "==", "SceneMate"),
        orderBy("name", "asc")
      )
      const querySnapshot = await getDocs(q)

      const files: ScriptFile[] = []
      querySnapshot.forEach((doc) => {
        const fileData = doc.data()
        files.push({
          id: doc.id,
          name: fileData.name || "Untitled Script",
          namespace: fileData.namespace || doc.id
        })
      })
      files.sort((a, b) => a.name.localeCompare(b.name))
      setScriptFiles(files)
      if (!activeTab && files.length > 0) {
        setActiveTab(files[0].id)
      }
      setLoading(false)
    } catch (err) {
      console.error("Error fetching script files:", err)
      setError(`Failed to load scripts: ${err instanceof Error ? err.message : "Unknown error"}`)
      setLoading(false)
    }
  }

  useEffect(() => {
    if (sessionStatus === "authenticated") {
      fetchScriptFiles()
    }
  }, [sessionStatus, userId, activeTab])

  const fetchFileDocumentId = async (namespace: string) => {
    if (!userId) return
    try {
      const filesRef = collection(db, `users/${userId}/files`)
      const q = query(filesRef, where("namespace", "==", namespace), limit(1))
      const querySnapshot = await getDocs(q)
      if (!querySnapshot.empty) {
        const fileDoc = querySnapshot.docs[0]
        setFileDocumentId(fileDoc.id)
        const fileData = fileDoc.data()
        if (fileData.name) {
          setFileName(fileData.name)
        }
      }
    } catch (err) {
      console.error("Error fetching file document ID:", err)
    }
  }

  const fetchScriptContent = async () => {
    if (!activeTab || !userId) {
      setError("No script selected or user not authenticated")
      return
    }

    setIsScriptLoading(true)
    setError(null)

    try {
      // Get file document to retrieve namespace
      const fileDocRef = doc(db, `users/${userId}/files/${activeTab}`)
      const fileDocSnap = await getDoc(fileDocRef)

      if (!fileDocSnap.exists()) {
        setError("Script file not found")
        setIsScriptLoading(false)
        return
      }

      const fileData = fileDocSnap.data()
      const fileNamespace = fileData.namespace || activeTab

      const chunksRef = collection(db, `users/${userId}/byteStoreCollection`)
      const q = query(chunksRef, where("metadata.doc_id", "==", fileNamespace))
      const querySnapshot = await getDocs(q)
      const chunks = querySnapshot.docs.map((d) => d.data())

      if (chunks.length === 0) {
        setError("No content found for this script")
        setIsScriptLoading(false)
        return
      }

      // Sort chunks by position or page_number
      if ("position" in chunks[0]) {
        chunks.sort((a, b) => (a.position || 0) - (b.position || 0))
      } else if ("metadata" in chunks[0] && "page_number" in chunks[0].metadata) {
        chunks.sort((a, b) => (a.metadata.page_number || 0) - (b.metadata.page_number || 0))
      }

      // Determine content field and assemble
      const contentField = "pageContent" in chunks[0] ? "pageContent" : "content"
      const content = chunks.map((chunk) => chunk[contentField] || "").join("\n")
      setScriptContent(content)
      setIsScriptReady(true)
    } catch (err) {
      console.error("Error fetching script content:", err)
      setError("Failed to load script content")
      setIsScriptLoading(false)
    } finally {
      setIsScriptLoading(false)
    }
  }

  const createNewChat = async (fileNamespace?: string, fileDocId?: string) => {
    if (!userId) {
      setError("Please sign in to create a new chat.")
      return null
    }

    try {
      let firstMessageText = "New Rehearsal"
      const chatsRef = collection(db, `users/${userId}/chats`)
      let actualFileDocId: string
      let actualNamespace: string

      if (fileNamespace && fileDocId) {
        actualNamespace = fileNamespace
        actualFileDocId = fileDocId
      } else if (fileDocId) {
        actualNamespace = fileDocId
        actualFileDocId = fileDocId
      } else if (fileNamespace) {
        actualNamespace = fileNamespace
        actualFileDocId = fileNamespace
      } else {
        const recentFile = await fetchMostRecentFile()
        if (recentFile) {
          actualNamespace = recentFile.namespace
          actualFileDocId = recentFile.id
          setFileName(recentFile.name)
        } else {
          setError("Please upload or select a file before creating a chat.")
          return null
        }
      }

      const chatData = {
        createdAt: serverTimestamp(),
        userId: userId,
        firstMessage: firstMessageText,
        lastUpdated: serverTimestamp(),
        fileNamespace: actualNamespace,
        fileDocumentId: actualFileDocId
      }

      const docRef = await addDoc(chatsRef, chatData)
      const newChatId = docRef.id
      setChatId(newChatId)
      setChatMessages([])
      setSelectedFileNamespace(actualNamespace)
      setFileDocumentId(actualFileDocId)
      return newChatId
    } catch (err) {
      if (isMounted.current) {
        setError(
          "Failed to create new chat: " +
            (err instanceof Error ? err.message : "Unknown error")
        )
      }
      return null
    }
  }

  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0]
      if (!file || !userId) {
        setError("No file selected or user not authenticated.")
        return
      }
      try {
        const docId = uuidv4()
        await handleUpload(file, null, userId, docId)
        const newChatId = await createNewChat(docId, docId)

        if (newChatId) {
          setSelectedFileNamespace(docId)
          setFileDocumentId(docId)
          setFileName(file.name)
          setChatId(newChatId)
          const messagesRef = collection(db, `users/${userId}/chats/${newChatId}/messages`)
          const welcomeMessageData = {
            role: "assistant",
            text: "File processed successfully! How can I assist with your script?",
            createdAt: serverTimestamp(),
            fileDocumentId: docId
          }
          const welcomeDocRef = await addDoc(messagesRef, welcomeMessageData)
          const initialMessage: ChatMessage = {
            id: welcomeDocRef.id,
            role: "assistant",
            content: welcomeMessageData.text,
            timestamp: new Date().toISOString(),
            fileDocumentId: docId
          }
          setChatMessages([initialMessage])
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Upload failed")
      }
    },
    [userId, handleUpload, createNewChat]
  )

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  useEffect(() => {
    if (activeSection === "script" && activeTab) {
      fetchScriptContent()
    }
  }, [activeSection, activeTab])

  // Placeholder for AI formatting (assuming scriptFormatter and markdownFormatterTool exist)
  useEffect(() => {
    if (isScriptReady && scriptContent && !isFormatting) {
      const formatScriptContent = async () => {
        setIsFormatting(true)
        try {
          // Simulate AI formatting (replace with actual implementation if tools are available)
          const formattedScript = {
            metadata: { title: fileName || "Untitled", author: "", characters: [], summary: "" },
            lines: scriptContent.split("\n").map((line, idx) => ({
              lineNumber: idx + 1,
              text: line,
            })),
          }
          setFormattedMarkdown(scriptContent)
        } catch (err) {
          console.error("Error formatting script:", err)
          setFormattedMarkdown(scriptContent)
        } finally {
          setIsFormatting(false)
        }
      }
      formatScriptContent()
    }
  }, [isScriptReady, scriptContent, isFormatting, fileName])

  useEffect(() => {
    return () => {
      isMounted.current = false
    }
  }, [])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm p-2 sm:p-4 md:p-0">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative w-full max-w-full sm:max-w-3xl md:max-w-4xl lg:max-w-6xl h-[90vh] sm:h-[85vh] bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 rounded-2xl shadow-2xl overflow-hidden border border-purple-500/20"
      >
        <div className="absolute inset-0 bg-gradient-to-t from-purple-500/5 to-transparent pointer-events-none" />

        <div className="absolute top-4 left-4 flex flex-col space-y-1">
          {sessionStatus === "loading" && (
            <div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Loader className="w-3 h-3 mr-1 animate-spin" />
              Loading session...
            </div>
          )}
          {sessionStatus === "unauthenticated" && (
            <div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Info className="w-3 h-3 mr-1" />
              Not authenticated
            </div>
          )}
          {apiConfigStatus === 'connecting' && (
            <div className="bg-yellow-500/20 text-yellow-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Loader className="w-3 h-3 mr-1 animate-spin" />
              Connecting to voice API...
            </div>
          )}
          {apiConfigStatus === 'invalid' && (
            <div className="bg-red-500/20 text-red-300 text-xs px-2 py-1 rounded-md flex items-center">
              <Info className="w-3 h-3 mr-1" />
              ElevenLabs API issue
            </div>
          )}
        </div>

        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-full transition-all duration-200 backdrop-blur-sm z-50"
        >
          <X className="w-5 h-5 sm:w-6 sm:h-6" />
        </button>

        <div className="flex flex-col md:flex-row h-full">
          <AnimatePresence>
            {(isMobileSidebarOpen || isOpen) && (
              <motion.div
                key="sidebar-motion"
                initial={{ x: -300, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -300, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <SideBar
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                  scriptFiles={scriptFiles}
                  loading={loading}
                  error={error}
                  setError={setError}
                  isUploading={isUploading}
                  uploadProgress={uploadProgress}
                  uploadStatusText={uploadStatusText}
                  handleUploadClick={handleUploadClick}
                  handleFileUpload={handleFileUpload}
                  fileInputRef={fileInputRef}
                  sessionStatus={sessionStatus}
                  session={session}
                  onClose={onClose}
                  isMobileSidebarOpen={isMobileSidebarOpen}
                  setIsMobileSidebarOpen={setIsMobileSidebarOpen}
                />
              </motion.div>
            )}
          </AnimatePresence>

          <div className="flex-1 overflow-hidden backdrop-blur-sm">
            <div className="h-full flex flex-col">
              <div className="border-b border-white/10 px-4 sm:px-6 py-3 sm:py-4 bg-black/20">
                <div className="flex flex-wrap gap-4 sm:space-x-6 pt-3">
                  {[
                    { name: "rehearsing", icon: <Mic className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                    { name: "chat", icon: <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                    { name: "script", icon: <Book className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                    { name: "details", icon: <FileText className="w-3 h-3 sm:w-4 sm:h-4 mr-1" /> },
                  ].map((section) => (
                    <button
                      key={section.name}
                      onClick={() => setActiveSection(section.name)}
                      className={`text-xs sm:text-sm font-medium px-2 py-1 border-b-2 transition-all duration-200 flex items-center ${
                        activeSection === section.name
                          ? "border-purple-500 text-purple-400"
                          : "border-transparent text-gray-400 hover:text-white hover:border-white/20"
                      }`}
                    >
                      {section.icon}
                      {section.name.charAt(0).toUpperCase() + section.name.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-4 sm:p-6 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`${activeTab}-${activeSection}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                    className="space-y-6 h-full"
                  >
                    {activeSection === "rehearsing" && (
                      <Rehearsals
                        apiConfigStatus={apiConfigStatus}
                        detailedErrorInfo={detailedErrorInfo}
                        isListening={isListening}
                        voiceStatus={voiceStatus}
                        isMuted={isMuted}
                        isSpeaking={isSpeaking}
                        hasPermission={hasPermission}
                        voiceErrorMessage={voiceErrorMessage}
                        toggleMute={toggleMute}
                        handleEndConversation={handleEndConversation}
                        handleStartConversation={handleStartConversation}
                        setVoiceErrorMessage={setVoiceErrorMessage}
                      />
                    )}

                    {activeSection === "chat" && <ChatTab chatId={activeTab ?? ''} namespace={namespace} />}

                    {activeSection === "script" && (
                      <ScriptTab
                        scriptContent={scriptContent}
                        isScriptLoading={isScriptLoading || isFormatting}
                        isScriptReady={isScriptReady && !isFormatting}
                        scriptName={fileName}
                        isListening={isListening}
                        isMuted={isMuted}
                        toggleMute={toggleMute}
                        handleEndConversation={handleEndConversation}
                      />
                    )}

                    {activeSection === "details" && (
                      <FileDetails
                        activeTab={activeTab}
                        fileName={fileName}
                        namespace={namespace}
                        apiConfigStatus={apiConfigStatus}
                        sessionStatus={sessionStatus}
                        detailedErrorInfo={detailedErrorInfo}
                      />
                    )}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export { Readermodal }