# ElevenLabs Automatic RAG Indexing Implementation

This document outlines the implementation of automatic RAG indexing for the CastMate application to ensure uploaded documents are immediately available for AI-powered voice rehearsals.

## Problem Solved

Previously, documents uploaded to ElevenLabs Knowledge Base required manual indexing in the ElevenLabs dashboard before they could be used by the conversational AI agent. This implementation adds automatic RAG indexing to the upload workflow.

## Changes Made

### 1. Fixed `computeRagIndex()` Function

**File: `components/scriptreaderAI/elevenlabs.ts`**

- ✅ **Updated API endpoints** to use correct ElevenLabs URLs:
  - Index trigger: `https://api.elevenlabs.io/v1/conversational-ai/rag-index`
  - Status check: `https://api.elevenlabs.io/v1/conversational-ai/rag-index-status`

- ✅ **Fixed request parameters** to use `documentation_id` instead of `documentationId`

- ✅ **Corrected status values** to match API response format:
  - "succeeded" (not "SUCCEEDED")
  - "failed" (not "FAILED")
  - "created", "processing" for intermediate states

- ✅ **Improved polling mechanism** with proper status endpoint usage

### 2. Created Complete Upload Workflow

**File: `components/scriptreaderAI/elevenlabs.ts`**

Added new function `uploadAndIndexForRehearsal()` that handles:

1. **Upload to Knowledge Base** - Upload document to ElevenLabs
2. **RAG Indexing** - Automatically trigger and wait for indexing completion
3. **Agent Association** - Associate document with the configured agent

```typescript
export async function uploadAndIndexForRehearsal(
  fileUrl: string,
  fileName: string,
  fileType: string,
  agentId: string,
  apiKey?: string
): Promise<{
  knowledgeBaseDocId: string;
  prompt_injectable: boolean;
  ragIndexStatus: string;
  ragIndexProgress: number;
  agentUpdated: boolean;
  uploaded_at: string;
}>
```

### 3. Updated Upload API Route

**File: `app/api/processScriptfile/route.ts`**

- ✅ **Replaced manual workflow** with streamlined `uploadAndIndexForRehearsal()` call
- ✅ **Enhanced logging** with detailed progress tracking
- ✅ **Improved error handling** with graceful fallbacks
- ✅ **Extended metadata storage** to include RAG indexing status

### 4. Enhanced Status Tracking

**File: `components/scriptreaderAI/useUpload.tsx`**

- ✅ **Added new status**: `INDEXING_RAG = "Indexing document for AI rehearsals..."`
- ✅ **Maintained existing workflow** with enhanced backend processing

## Technical Details

### API Endpoints Used

```typescript
// Trigger RAG indexing
POST https://api.elevenlabs.io/v1/conversational-ai/rag-index
{
  "documentation_id": "doc_id_here",
  "model": "e5_mistral_7b_instruct",
  "force_reindex": false
}

// Check indexing status
POST https://api.elevenlabs.io/v1/conversational-ai/rag-index-status
{
  "documentation_id": "doc_id_here"
}
```

### Status Flow

1. **"created"** - Indexing job created
2. **"processing"** - Indexing in progress
3. **"succeeded"** - Indexing completed successfully
4. **"failed"** - Indexing failed

### Polling Configuration

- **Max attempts**: 30 (150 seconds total timeout)
- **Polling interval**: 5 seconds
- **Timeout handling**: Graceful error with detailed message

## User Experience Improvements

### Before Implementation
1. User uploads script
2. Script appears in Knowledge Base
3. **Manual step required**: User must manually index in ElevenLabs dashboard
4. Script becomes available for rehearsals

### After Implementation
1. User uploads script
2. Script automatically uploads to Knowledge Base
3. **Automatic RAG indexing** happens in background
4. Script immediately available for rehearsals

## Error Handling

### Graceful Degradation
- If RAG indexing fails, the upload process continues
- Detailed error logging for debugging
- User receives completion notification regardless
- Manual indexing can still be performed if needed

### Comprehensive Logging
All operations are logged with `[ELEVENLABS]` prefix:
- Upload progress
- Indexing status updates
- Agent association results
- Error details with context

## Firestore Metadata Storage

Enhanced metadata now includes:

```typescript
{
  knowledgeBaseDocId: string,
  prompt_injectable: boolean,
  ragIndexStatus: "succeeded" | "failed" | "processing",
  ragIndexProgress: number,
  agentUpdated: boolean,
  uploaded_at: string
}
```

## Testing Verification

To verify the implementation works:

1. **Upload a script** through the CastMate interface
2. **Check console logs** for `[ELEVENLABS]` messages
3. **Verify in ElevenLabs dashboard** that document appears as indexed
4. **Test voice rehearsal** to confirm script content is accessible

## Environment Variables Required

```bash
ELEVENLABS_API_KEY=your_api_key_here
ELEVENLABS_AGENT_ID=your_agent_id_here
```

## Benefits

✅ **Zero manual intervention** required for script indexing
✅ **Immediate availability** for AI rehearsals
✅ **Robust error handling** with detailed logging
✅ **Backward compatibility** with existing upload workflow
✅ **Enhanced user experience** with automatic processing
✅ **Production ready** with proper timeout and retry mechanisms

The implementation ensures that every script uploaded to CastMate is automatically indexed and immediately available for AI-powered voice rehearsals without any manual steps required.
