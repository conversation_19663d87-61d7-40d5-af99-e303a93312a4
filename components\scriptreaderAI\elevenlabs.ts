import { ElevenLabsClient, elevenlabs } from "elevenlabs";

// Assuming elevenlabs.KnowledgeBaseDocumentResponse is the SDK type for document upload
// and elevenlabs.KnowledgeBaseDocument is the SDK type for fetching a document.
// If these are not exact, adjust based on your SDK version.

/**
 * Type definition for the custom response after uploading a document to ElevenLabs knowledge base.
 */
interface CustomKnowledgeBaseUploadResponse {
  document_id: string;
  knowledge_base_id: string;
}

/**
 * Type definition for ElevenLabs RAG index status response
 * Matches the return structure of GET /v1/knowledge-base/{kb_id}/documents/{doc_id}/index-status
 */
interface RagIndexResponse {
  status: "indexed" | "not_indexed" | "indexing" | "failed" | string; // string for broader compatibility
  message?: string;
  // progress_percentage is not typically part of this response for the new endpoints.
}

/**
 * Type definition for a knowledge base document's details.
 * This might be similar to elevenlabs.KnowledgeBaseDocument from the SDK.
 */
interface KnowledgeBaseDocumentDetail {
  document_id: string;
  knowledge_base_id: string;
  status: string;
  name: string;
  mime_type: string;
  source_type: string;
  source_url?: string | null;
  size_bytes: number;
  created_at_unix: number;
  metadata?: Record<string, any>;
  content_chunks_count?: number;
  indexed_chunks_count?: number;
  indexing_status?: "indexed" | "not_indexed" | "indexing" | "failed";
  prompt_injectable?: boolean; // This is the field we often need
}


/**
 * Creates and configures an instance of the ElevenLabs client
 * @param apiKey - Optional API key to override the environment variable
 * @returns Configured ElevenLabs client instance
 */
export function createElevenLabsClient(apiKey?: string): ElevenLabsClient {
  const key = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';
  if (!key) {
    throw new Error("ElevenLabs API key is required. Set ELEVENLABS_API_KEY in your environment variables.");
  }
  return new ElevenLabsClient({ apiKey: key });
}

/**
 * Uploads a document to ElevenLabs knowledge base
 *
 * This function fetches a file from a URL and uploads it to the ElevenLabs
 * knowledge base system, making it available for association with agents.
 *
 * @param fileUrl - URL of the file to upload
 * @param fileName - Name of the file for identification
 * @param fileType - MIME type of the file
 * @param apiKey - Optional API key to override the environment variable
 * @returns Object containing document_id and knowledge_base_id
 * @throws Error if file type is unsupported, URL fetching fails, or API errors occur
 */
export async function uploadToKnowledgeBase(
  fileUrl: string,
  fileName: string,
  fileType: string,
  apiKey?: string
): Promise<CustomKnowledgeBaseUploadResponse> {
  try {
    const client = createElevenLabsClient(apiKey);

    const supportedTypes = [
      "application/pdf",
      "text/plain",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/markdown",
    ];

    if (!supportedTypes.includes(fileType)) {
      throw new Error(`Unsupported file type: ${fileType}. Supported types: ${supportedTypes.join(", ")}`);
    }

    let fileResponse;
    try {
      fileResponse = await fetch(fileUrl);
      if (!fileResponse.ok) {
        throw new Error(`HTTP error ${fileResponse.status}: ${fileResponse.statusText}`);
      }
    } catch (fetchError) {
      throw new Error(`Failed to fetch file: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`);
    }

    let fileBlob;
    try {
      fileBlob = await fileResponse.blob();
      if (fileBlob.size === 0) {
        throw new Error("Fetched file is empty (0 bytes)");
      }
    } catch (blobError) {
      throw new Error(`Failed to process file data: ${blobError instanceof Error ? blobError.message : String(blobError)}`);
    }

    const file = new File([fileBlob], fileName, { type: fileType });

    try {
      // Use the SDK's method for adding to knowledge base.
      // The type `elevenlabs.KnowledgeBaseDocumentResponse` should be confirmed from SDK.
      // It is expected to return at least document_id and knowledge_base_id.
      const response: elevenlabs.KnowledgeBaseDocumentResponse = await client.conversationalAi.addToKnowledgeBase({
        file,
        name: fileName,
      });

      if (!response || !response.document_id || !response.knowledge_base_id) {
        console.error("[ELEVENLABS] Invalid response from addToKnowledgeBase:", response);
        throw new Error("Invalid response from ElevenLabs API during upload: Missing document_id or knowledge_base_id");
      }

      return {
        document_id: response.document_id,
        knowledge_base_id: response.knowledge_base_id,
      };
    } catch (apiError) {
      if (apiError instanceof Error) {
        throw new Error(`ElevenLabs API error during upload: ${apiError.message}`);
      }
      throw new Error(`Unknown error from ElevenLabs API during upload: ${String(apiError)}`);
    }
  } catch (error) {
    console.error("[ELEVENLABS] Error uploading to ElevenLabs Knowledge Base:", error);
    throw error;
  }
}

/**
 * Triggers RAG indexing for a knowledge base document
 *
 * This function initiates RAG indexing for the specified document and waits until
 * the indexing process is complete (status: indexed).
 *
 * @param knowledgeBaseId - ID of the knowledge base containing the document
 * @param documentId - ID of the knowledge base document to index
 * @param apiKey - Optional API key to override the environment variable
 * @param forceReindex - Whether to force reindexing (Note: API endpoint might not support this directly, verify with ElevenLabs documentation)
 * @returns RAG index status
 * @throws Error if RAG indexing fails or times out
 */
export async function computeRagIndex(
  knowledgeBaseId: string,
  documentId: string,
  apiKey?: string,
  forceReindex: boolean = false // Note: The documented POST .../index endpoint doesn't take a body or parameters like forceReindex.
                                // This parameter is kept for conceptual completeness but may not be functional.
): Promise<RagIndexResponse> {
  try {
    if (!knowledgeBaseId || typeof knowledgeBaseId !== 'string') {
      throw new Error("Knowledge Base ID is required and must be a string");
    }
    if (!documentId || typeof documentId !== 'string') {
      throw new Error("Document ID is required and must be a string");
    }

    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';

    // Endpoints based on official API documentation
    const indexTriggerUrl = `https://api.elevenlabs.io/v1/knowledge-base/${knowledgeBaseId}/documents/${documentId}/index`;
    const statusCheckUrl = `https://api.elevenlabs.io/v1/knowledge-base/${knowledgeBaseId}/documents/${documentId}/index-status`;

    console.log(`[ELEVENLABS] Triggering RAG indexing for document ${documentId} in knowledge base ${knowledgeBaseId}${forceReindex ? " (force reindex requested)" : ""}`);

    // The documented POST .../index endpoint does not specify a request body.
    const initialResponse = await fetch(indexTriggerUrl, {
      method: "POST",
      headers: {
        "xi-api-key": apiKeyToUse,
        // "Content-Type": "application/json", // Not typically needed for an empty body POST
      },
      // body: JSON.stringify({}), // If the API expects an empty JSON body. Docs suggest no body.
    });

    if (!initialResponse.ok) {
      const errorText = await initialResponse.text();
      console.error(`[ELEVENLABS] RAG indexing trigger failed:`, {
        status: initialResponse.status, statusText: initialResponse.statusText, url: indexTriggerUrl,
        knowledgeBaseId, documentId, errorResponse: errorText
      });
      throw new Error(`Failed to trigger RAG indexing: ${initialResponse.status} ${initialResponse.statusText} - ${errorText}`);
    }
    
    // The trigger call might return 202 Accepted or some initial status.
    // We proceed to poll regardless.
    console.log(`[ELEVENLABS] RAG indexing trigger call successful. Response status: ${initialResponse.status}. Polling status...`);
    await new Promise(resolve => setTimeout(resolve, 2000)); // Small delay before first poll

    const maxAttempts = 60; // Increased attempts for longer indexing (60 * 5s = 300s = 5 mins timeout)
    const pollingInterval = 5000; // Poll every 5 seconds
    let attempts = 0;
    let result: RagIndexResponse;

    while (attempts < maxAttempts) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, pollingInterval));

      const statusResponse = await fetch(statusCheckUrl, {
        method: "GET",
        headers: { "xi-api-key": apiKeyToUse },
      });

      if (!statusResponse.ok) {
        const errorText = await statusResponse.text();
        console.error(`[ELEVENLABS] RAG indexing status check failed:`, {
          status: statusResponse.status, statusText: statusResponse.statusText, url: statusCheckUrl,
          documentId, knowledgeBaseId, attempt: attempts, errorResponse: errorText
        });
        throw new Error(`Failed to check RAG indexing status: ${statusResponse.status} ${statusResponse.statusText} - ${errorText}`);
      }

      result = await statusResponse.json();
      console.log(`[ELEVENLABS] RAG indexing status (attempt ${attempts}/${maxAttempts}):`, result);

      if (result.status === "indexed") {
        console.log(`[ELEVENLABS] RAG indexing completed successfully for document ${documentId}`);
        return result;
      }
      if (result.status === "failed") {
        throw new Error(`RAG indexing failed for document ${documentId}: ${result.message || 'No additional message'}`);
      }
      // Continue polling if status is "indexing", "not_indexed", or other intermediate states.
    }

    throw new Error(`RAG indexing for document ${documentId} in KB ${knowledgeBaseId} did not complete within the expected time (${maxAttempts * pollingInterval / 1000} seconds)`);
  } catch (error) {
    console.error("[ELEVENLABS] Error during RAG indexing:", error);
    throw error;
  }
}

/**
 * Fetches the agent's configuration to inspect its settings
 *
 * @param agentId - ID of the agent to fetch
 * @param apiKey - Optional API key to override the environment variable
 * @returns Agent configuration
 * @throws Error if the fetch fails
 */
export async function getAgentConfiguration(agentId: string, apiKey?: string): Promise<any> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }

    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';

    const response = await fetch(
      `https://api.elevenlabs.io/v1/convai/agents/${encodeURIComponent(agentId)}`,
      {
        method: "GET",
        headers: {
          "xi-api-key": apiKeyToUse,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to fetch agent configuration: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log(`[ELEVENLABS] Fetched agent configuration for agent ${agentId}:`, result);
    return result;
  } catch (error) {
    console.error(`[ELEVENLABS] Error fetching agent configuration for agent ${agentId}:`, error);
    throw error;
  }
}

/**
 * Associates a knowledge base document with an agent
 *
 * This function updates the agent to include the document in its knowledge base
 * using the document_ids field and enables RAG.
 *
 * @param agentId - ID of the agent to associate with the document
 * @param knowledgeBaseDocId - ID of the knowledge base document (this is the `document_id`)
 * @param apiKey - Optional API key to override the environment variable
 * @returns Result of the agent update operation
 * @throws Error if RAG indexing or agent update fails
 */
export async function updateAgentKnowledgeBase(
  agentId: string,
  knowledgeBaseDocId: string, // This is the document_id
  apiKey?: string
): Promise<any> {
  try {
    if (!agentId || typeof agentId !== 'string') {
      throw new Error("Agent ID is required and must be a string");
    }
    if (!knowledgeBaseDocId || typeof knowledgeBaseDocId !== 'string') {
      throw new Error("Knowledge base document ID (document_id) is required and must be a string");
    }

    console.log(`[ELEVENLABS] Associating document ${knowledgeBaseDocId} with agent ${agentId}`);

    const currentConfig = await getAgentConfiguration(agentId, apiKey);
    const currentConversationConfig = currentConfig.conversation_config || {};
    
    // Ensure existing document_ids are preserved and the new one is added (if not already present)
    const existingDocumentIds = currentConfig.document_ids || [];
    const updatedDocumentIds = [...new Set([...existingDocumentIds, knowledgeBaseDocId])];


    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';
    console.log(`[ELEVENLABS] Attempting to update agent ${agentId} to include knowledge base document ${knowledgeBaseDocId}`);

    const maxRetries = 3;
    let attempt = 0;
    let updateResult = null;

    while (attempt < maxRetries) {
      attempt++;
      try {
        const updateResponse = await fetch(
          `https://api.elevenlabs.io/v1/convai/agents/${encodeURIComponent(agentId)}`,
          {
            method: "PATCH",
            headers: {
              "xi-api-key": apiKeyToUse,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              document_ids: updatedDocumentIds,
              conversation_config: {
                ...currentConversationConfig,
                rag_enabled: true,
              },
              // apply_changes: true, // This field might be specific or not needed; official docs are key
            }),
          }
        );

        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          throw new Error(`Failed to update agent (attempt ${attempt}/${maxRetries}): ${updateResponse.status} ${updateResponse.statusText} - ${errorText}`);
        }

        updateResult = await updateResponse.json();
        console.log(`[ELEVENLABS] Successfully updated agent with document_ids and RAG settings (attempt ${attempt}/${maxRetries})`, updateResult);
        break; 
      } catch (error) {
        if (attempt === maxRetries) {
          throw error; 
        }
        console.warn(`[ELEVENLABS] Update attempt ${attempt}/${maxRetries} failed, retrying after delay...`, error);
        await new Promise(resolve => setTimeout(resolve, 2000)); 
      }
    }

    const updatedAgentConfig = await getAgentConfiguration(agentId, apiKey);
    console.log(`[ELEVENLABS] Agent ${agentId} configuration after update:`, updatedAgentConfig);

    console.log(`[ELEVENLABS] Document ${knowledgeBaseDocId} associated with agent ${agentId} and RAG enabled. Please verify the association in the ElevenLabs dashboard and test the agent in rehearsal.`);
    return updateResult;
  } catch (error) {
    console.error("[ELEVENLABS] Error updating agent knowledge base:", error);
    throw new Error(
      `Failed to associate knowledge base document ${knowledgeBaseDocId} with agent ${agentId}. ` +
      `Error: ${error instanceof Error ? error.message : String(error)}. ` +
      `Manual association may be required via the ElevenLabs dashboard.`
    );
  }
}

/**
 * Retrieves a list of all knowledge bases for the account.
 *
 * @param apiKey - Optional API key to override the environment variable
 * @returns List of knowledge bases
 * @throws Error if API request fails
 */
export async function getKnowledgeBaseList(apiKey?: string): Promise<any[]> {
  try {
    const client = createElevenLabsClient(apiKey);
    // This SDK method should list knowledge bases, not individual documents across all KBs.
    const knowledgeBases = await client.conversationalAi.getKnowledgeBaseList();

    if (!Array.isArray(knowledgeBases)) {
      console.warn("[ELEVENLABS] Unexpected response from getKnowledgeBaseList:", knowledgeBases);
      return [];
    }

    return knowledgeBases;
  } catch (error) {
    console.error("[ELEVENLABS] Error getting knowledge base list:", error);
    throw error;
  }
}

/**
 * Retrieves a knowledge base document by ID and knowledge base ID.
 *
 * @param knowledgeBaseId - ID of the knowledge base containing the document
 * @param documentId - ID of the document to retrieve
 * @param apiKey - Optional API key to override the environment variable
 * @returns Knowledge base document details (includes prompt_injectable)
 * @throws Error if document doesn't exist or API request fails
 */
export async function getKnowledgeBaseDocument(
  knowledgeBaseId: string,
  documentId: string,
  apiKey?: string
): Promise<KnowledgeBaseDocumentDetail> {
  if (!knowledgeBaseId || typeof knowledgeBaseId !== 'string') {
    throw new Error("Knowledge Base ID is required and must be a string");
  }
  if (!documentId || typeof documentId !== 'string') {
    throw new Error("Document ID is required and must be a string");
  }

  try {
    const client = createElevenLabsClient(apiKey);
    // The SDK method getKnowledgeBaseDocumentById requires both IDs.
    // The return type should be similar to `elevenlabs.KnowledgeBaseDocument`.
    const document: KnowledgeBaseDocumentDetail = await client.conversationalAi.getKnowledgeBaseDocumentById(documentId, knowledgeBaseId);

    if (!document || !document.document_id) {
      throw new Error(`Document with ID ${documentId} in KB ${knowledgeBaseId} not found or has invalid format`);
    }

    return document;
  } catch (error) {
    console.error(`[ELEVENLABS] Error getting knowledge base document ${documentId} from KB ${knowledgeBaseId}:`, error);
    throw error;
  }
}

/**
 * Complete upload workflow: Upload to Knowledge Base + RAG Indexing + Agent Association
 *
 * This function handles the complete workflow for making a document available for AI rehearsals:
 * 1. Upload to ElevenLabs Knowledge Base
 * 2. Fetch document details (for prompt_injectable)
 * 3. Trigger RAG indexing
 * 4. Associate with agent
 *
 * @param fileUrl - URL of the file to upload
 * @param fileName - Name of the file for identification
 * @param fileType - MIME type of the file
 * @param agentId - ID of the agent to associate with the document
 * @param apiKey - Optional API key to override the environment variable
 * @returns Complete upload result with all metadata
 * @throws Error if any step fails
 */
export async function uploadAndIndexForRehearsal(
  fileUrl: string,
  fileName: string,
  fileType: string,
  agentId: string,
  apiKey?: string
): Promise<{
  knowledgeBaseDocId: string; // This is document_id
  knowledgeBaseId: string;    // New field
  prompt_injectable: boolean;
  ragIndexStatus: string;
  agentUpdated: boolean;
  uploaded_at: string;
}> {
  try {
    console.log(`[ELEVENLABS] Starting complete upload workflow for ${fileName}`);

    // Step 1: Upload to Knowledge Base
    console.log(`[ELEVENLABS] Step 1: Uploading to Knowledge Base...`);
    const kbUploadResponse = await uploadToKnowledgeBase(fileUrl, fileName, fileType, apiKey);
    console.log(`[ELEVENLABS] Knowledge Base upload successful: doc_id=${kbUploadResponse.document_id}, kb_id=${kbUploadResponse.knowledge_base_id}`);

    // Step 2: Fetch document details to get prompt_injectable
    console.log(`[ELEVENLABS] Step 2: Fetching document details for prompt_injectable...`);
    const docDetails = await getKnowledgeBaseDocument(kbUploadResponse.knowledge_base_id, kbUploadResponse.document_id, apiKey);
    const promptInjectable = docDetails.prompt_injectable === true; // Ensure boolean
    console.log(`[ELEVENLABS] Document prompt_injectable: ${promptInjectable}`);

    // Step 3: Trigger RAG Indexing
    console.log(`[ELEVENLABS] Step 3: Starting RAG indexing...`);
    const ragIndexResult = await computeRagIndex(
      kbUploadResponse.knowledge_base_id,
      kbUploadResponse.document_id,
      apiKey,
      false // forceReindex
    );
    console.log(`[ELEVENLABS] RAG indexing completed: ${ragIndexResult.status} (${ragIndexResult.message || ''})`);

    // Step 4: Associate with Agent
    console.log(`[ELEVENLABS] Step 4: Associating with agent ${agentId}...`);
    await updateAgentKnowledgeBase(agentId, kbUploadResponse.document_id, apiKey);
    console.log(`[ELEVENLABS] Agent association completed successfully`);

    const result = {
      knowledgeBaseDocId: kbUploadResponse.document_id,
      knowledgeBaseId: kbUploadResponse.knowledge_base_id,
      prompt_injectable: promptInjectable,
      ragIndexStatus: ragIndexResult.status,
      agentUpdated: true, // Assuming success if no error thrown by updateAgentKnowledgeBase
      uploaded_at: new Date().toISOString()
    };

    console.log(`[ELEVENLABS] Complete upload workflow finished successfully for ${fileName}`, result);
    return result;

  } catch (error) {
    console.error(`[ELEVENLABS] Complete upload workflow failed for ${fileName}:`, error);
    throw error;
  }
}

/**
 * Test function to verify RAG indexing API endpoints using the corrected structure.
 *
 * @param knowledgeBaseId - ID of an existing knowledge base
 * @param documentId - ID of an existing document within that knowledge base to test with
 * @param apiKey - Optional API key to override the environment variable
 * @returns Test results
 */
export async function testRagIndexingEndpoints(
  knowledgeBaseId: string,
  documentId: string,
  apiKey?: string
): Promise<any> {
  try {
    console.log(`[ELEVENLABS] Testing RAG indexing endpoints for document ${documentId} in KB ${knowledgeBaseId}`);

    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';

    // Test 1: Try to trigger indexing
    const indexTriggerUrl = `https://api.elevenlabs.io/v1/knowledge-base/${knowledgeBaseId}/documents/${documentId}/index`;
    console.log(`[ELEVENLABS] Testing trigger endpoint: POST ${indexTriggerUrl}`);

    const triggerResponse = await fetch(indexTriggerUrl, {
      method: "POST",
      headers: {
        "xi-api-key": apiKeyToUse,
      },
    });

    console.log(`[ELEVENLABS] Trigger response status: ${triggerResponse.status}`);
    const triggerResultText = await triggerResponse.text();
    let triggerResultJson = null;
    try {
      triggerResultJson = triggerResultText ? JSON.parse(triggerResultText) : null;
    } catch (e) { /* ignore parsing error, use text */ }
    
    console.log(`[ELEVENLABS] Trigger response body:`, triggerResultJson || triggerResultText);


    // Test 2: Try to check status
    const statusCheckUrl = `https://api.elevenlabs.io/v1/knowledge-base/${knowledgeBaseId}/documents/${documentId}/index-status`;
    console.log(`[ELEVENLABS] Testing status endpoint: GET ${statusCheckUrl}`);

    const statusResponse = await fetch(statusCheckUrl, {
      method: "GET",
      headers: {
        "xi-api-key": apiKeyToUse,
      },
    });

    console.log(`[ELEVENLABS] Status response status: ${statusResponse.status}`);
    const statusResultText = await statusResponse.text();
    let statusResultJson = null;
    try {
      statusResultJson = statusResultText ? JSON.parse(statusResultText) : null;
    } catch (e) { /* ignore parsing error, use text */ }

    console.log(`[ELEVENLABS] Status response body:`, statusResultJson || statusResultText);

    return {
      trigger: {
        status: triggerResponse.status,
        ok: triggerResponse.ok,
        result: triggerResultJson || triggerResultText
      },
      statusCheck: {
        status: statusResponse.status,
        ok: statusResponse.ok,
        result: statusResultJson || statusResultText
      }
    };

  } catch (error) {
    console.error(`[ELEVENLABS] Error testing RAG indexing endpoints:`, error);
    throw error;
  }
}