import { NextRequest, NextResponse } from "next/server";
import { setDoc, doc as firestoreDoc } from "firebase/firestore";
import { db } from "../../../components/firebase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { Pinecone } from "@pinecone-database/pinecone";
import { FirestoreStore } from "../../../lib/FirestoreStore";
import { processDocument } from "components/DocViewer/documentProcessors";
import { createGroqClient } from "../../../lib/llms/groq";
import { uploadAndIndexForRehearsal } from "components/scriptreaderAI/elevenlabs";
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]/authOptions";

const ELEVENLABS_AGENT_ID = process.env.ELEVENLABS_AGENT_ID || "1WU4LPk9482VXQFb80aq";

interface Document {
  pageContent: string;
  metadata: Record<string, any>;
}

// Utility function for consistent logging
function logProcess(stage: string, message: string, data?: any) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [DOCUMENT_PROCESSING] [${stage}] ${message}`);
  if (data) {
    console.log(`[${timestamp}] [DOCUMENT_PROCESSING] [${stage}] Data:`, JSON.stringify(data, null, 2));
  }
}

function cleanMetadata(metadata: any): Record<string, any> {
  const cleaned: Record<string, any> = {};
  for (const [key, value] of Object.entries(metadata)) {
    cleaned[key] = value ?? (key === 'sectionTitle' ? 'No Title' : key === 'questions' ? [] : key === 'is_summary' ? false : '');
  }
  return cleaned;
}

function createErrorMetadata(error: any, docInfo: { docId: string; fileName: string; fileType: string; category?: string; chunkId?: string }): Record<string, any> {
  return cleanMetadata({
    documentId: docInfo.docId,
    document_title: docInfo.fileName,
    file_type: docInfo.fileType,
    category: docInfo.category || 'Uncategorized',
    chunk_id: docInfo.chunkId || docInfo.docId,
    error_message: error instanceof Error ? error.message : 'Unknown error',
    error_details: error.toString(),
    created_at: new Date().toISOString(),
    sectionTitle: 'Processing Error',
    status: 'failed',
    is_error: true,
    processing_stage: 'document_processing'
  });
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  logProcess("REQUEST_RECEIVED", "Document processing request received");

  // Validate NextAuth session
  const session = await getServerSession(authOptions);
  if (!session?.user?.email) {
    logProcess("AUTH_ERROR", "No valid session found");
    return NextResponse.json({ success: false, error: "Unauthorized - No valid session" }, { status: 401 });
  }

  const requestData = await req.json();
  const { docId, userId, fileName, fileType, fileUrl } = requestData;

  logProcess("REQUEST_PARAMS", "Request parameters extracted", {
    docId,
    userId,
    fileName,
    fileType,
    hasFileUrl: !!fileUrl
  });

  try {
    // Validate required parameters
    logProcess("VALIDATION", "Validating required parameters");
    if (!docId || !userId || !fileName || !fileType || !fileUrl) {
      logProcess("VALIDATION_ERROR", "Missing required parameters", {
        hasDocId: !!docId,
        hasUserId: !!userId,
        hasFileName: !!fileName,
        hasFileType: !!fileType,
        hasFileUrl: !!fileUrl
      });
      throw new Error("Missing required parameters");
    }

    // Validate that userId matches session email
    if (userId !== session.user.email) {
      logProcess("AUTH_ERROR", "User ID mismatch", {
        sessionEmail: session.user.email,
        requestUserId: userId
      });
      throw new Error("Unauthorized - User ID mismatch");
    }
    logProcess("VALIDATION_SUCCESS", "All required parameters present");

    // Initialize Groq client with userId
    logProcess("INIT", "Initializing Groq client");
    const groq = createGroqClient({ userEmail: userId });
    logProcess("INIT_SUCCESS", "Groq client initialized");

    // Initialize embeddings and storage
    logProcess("INIT", "Initializing embeddings and storage");
    const embeddings = new OpenAIEmbeddings({ openAIApiKey: process.env.OPENAI_API_KEY! });
    const pinecone = new Pinecone();
    const pineconeIndex = pinecone.Index(process.env.PINECONE_scenemate_INDEX!);
    const byteCollection = `users/${userId}/byteStoreCollection`;
    const firestoreStore = new FirestoreStore({ collectionPath: byteCollection });
    const category = "SceneMate";
    logProcess("INIT_SUCCESS", "Embeddings and storage initialized");

    // Process the document
    logProcess("DOCUMENT_PROCESSING", "Starting document processing");
    const fileResponse = await fetch(fileUrl);
    if (!fileResponse.ok) {
      logProcess("DOCUMENT_FETCH_ERROR", `Failed to fetch file: ${fileResponse.statusText}`);
      throw new Error(`Failed to fetch file: ${fileResponse.statusText}`);
    }

    const fileBlob = await fileResponse.blob();
    const file = new File([fileBlob], fileName, { type: fileType });
    logProcess("DOCUMENT_PROCESSING", "File fetched, processing content");

    const processedContent = await processDocument(file, docId, fileType, fileName, userId, category, '', 1500, 200);
    logProcess("DOCUMENT_PROCESSING_COMPLETE", "Document processing completed", {
      chunks: processedContent.length
    });

    if (!processedContent.length) {
      logProcess("CONTENT_ERROR", "No content extracted from file");
      throw new Error('No content extracted');
    }

    // Prepare data for Firestore
    logProcess("FIRESTORE_PREPARATION", "Preparing data for Firestore");
    const byteStoreData: [string, Document][] = processedContent.map(doc => [
      doc.metadata.chunk_id,
      {
        pageContent: doc.pageContent,
        metadata: cleanMetadata({
          ...doc.metadata,
          document_title: fileName,
          category,
          file_type: fileType,
          fileUrl,
          processed_at: new Date().toISOString()
        })
      }
    ]);

    logProcess("FIRESTORE_STORAGE", "Storing documents in Firestore");
    await firestoreStore.mset(byteStoreData);
    logProcess("FIRESTORE_STORAGE_COMPLETE", "Documents stored in Firestore");

    // Upsert embeddings to Pinecone
    logProcess("PINECONE_EMBEDDINGS", "Generating and storing embeddings in Pinecone");
    for (const doc of processedContent) {
      logProcess("EMBEDDING_GENERATION", `Generating embedding for chunk ${doc.metadata.chunk_id}`);
      const embedding = await embeddings.embedQuery(doc.pageContent);
      const metadataForPinecone = cleanMetadata({
        content: doc.pageContent,
        doc_id: doc.metadata.doc_id,
        chunk_id: doc.metadata.chunk_id,
        document_title: fileName,
        category,
        file_type: fileType,
        sectionTitle: doc.metadata.sectionTitle,
        questions: doc.metadata.questions,
        is_summary: doc.metadata.is_summary
      });

      logProcess("PINECONE_UPSERT", `Upserting embedding for chunk ${doc.metadata.chunk_id}`);
      await pineconeIndex.namespace(docId).upsert([{ id: doc.metadata.chunk_id, values: embedding, metadata: metadataForPinecone }]);
    }
    logProcess("PINECONE_EMBEDDINGS_COMPLETE", "All embeddings stored in Pinecone");

    // Always upload to ElevenLabs Knowledge Base
    logProcess("ELEVENLABS_CHECK", "Checking if ElevenLabs upload can proceed", {
      hasAgentId: !!ELEVENLABS_AGENT_ID
    });

    let elevenLabsData = null;
    if (ELEVENLABS_AGENT_ID) {
      logProcess("ELEVENLABS_UPLOAD_START", "Starting ElevenLabs Knowledge Base upload process", {
        fileName,
        fileType,
        agentId: ELEVENLABS_AGENT_ID
      });

      try {
        logProcess("ELEVENLABS_WORKFLOW_START", `Starting complete ElevenLabs workflow for ${fileName}...`);

        const elevenLabsResult = await uploadAndIndexForRehearsal(
          fileUrl,
          fileName,
          fileType,
          ELEVENLABS_AGENT_ID
        );

        logProcess("ELEVENLABS_WORKFLOW_SUCCESS", "Complete ElevenLabs workflow finished successfully", {
          knowledgeBaseDocId: elevenLabsResult.knowledgeBaseDocId,
          ragIndexStatus: elevenLabsResult.ragIndexStatus,
          ragIndexProgress: elevenLabsResult.ragIndexProgress,
          agentUpdated: elevenLabsResult.agentUpdated
        });

        elevenLabsData = {
          knowledgeBaseDocId: elevenLabsResult.knowledgeBaseDocId,
          prompt_injectable: elevenLabsResult.prompt_injectable,
          ragIndexStatus: elevenLabsResult.ragIndexStatus,
          ragIndexProgress: elevenLabsResult.ragIndexProgress,
          agentUpdated: elevenLabsResult.agentUpdated,
          uploaded_at: elevenLabsResult.uploaded_at
        };

        // Store ElevenLabs metadata in Firestore
        logProcess("ELEVENLABS_METADATA_STORAGE", "Storing ElevenLabs metadata in Firestore");
        await setDoc(
          firestoreDoc(db, "users", userId, "ElevenLabsData", docId),
          elevenLabsData
        );

        logProcess("ELEVENLABS_PROCESS_COMPLETE", `Successfully uploaded to ElevenLabs Knowledge Base with ID: ${knowledgeBaseResponse.id}`);
      } catch (elevenLabsError) {
        logProcess("ELEVENLABS_ERROR", "Error uploading to ElevenLabs", {
          error: elevenLabsError instanceof Error ? elevenLabsError.message : String(elevenLabsError)
        });
        // Don't throw here, as we still want to return success for the rest of the processing
        elevenLabsData = {
          error: elevenLabsError instanceof Error ? elevenLabsError.message : String(elevenLabsError),
          error_at: new Date().toISOString()
        };
      }
    } else {
      logProcess("ELEVENLABS_SKIPPED", "Skipping ElevenLabs upload", {
        reason: "No agent ID configured"
      });
    }

    logProcess("PROCESS_COMPLETE", "Document processing completed successfully");
    return NextResponse.json({
      success: true,
      elevenLabs: elevenLabsData
    });
  } catch (error: any) {
    logProcess("ERROR", "Error during document processing", {
      error: error instanceof Error ? error.message : String(error)
    });
    const errorMetadata = createErrorMetadata(error, { docId, fileName, fileType, category: "SceneMate" });

    logProcess("ERROR_METADATA", "Storing error metadata in Firestore");
    await setDoc(firestoreDoc(db, "users", userId, "MetadataFallback", docId), errorMetadata);

    logProcess("ERROR_RESPONSE", "Returning error response");
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}

export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}