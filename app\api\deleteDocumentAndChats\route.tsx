import { NextRequest, NextResponse } from 'next/server';

// Export a named handler for POST method
export async function POST(req: NextRequest) {
  const { userId, namespace } = await req.json(); // Extract userId and namespace from the request body

  try {
    // Dynamic import to prevent build-time issues
    const { deleteDocumentAndChatsByNamespace } = await import('../../../components/DocViewer/deleteDocumentsAndChatsByNamespace ');
    await deleteDocumentAndChatsByNamespace(userId, namespace);
    return NextResponse.json({ message: 'Document and chats successfully deleted.' }, { status: 200 });
  } catch (error) {
    console.error('Error deleting document and chats:', error);
    return NextResponse.json({ error: 'Error deleting document and chats.' }, { status: 500 });
  }
}

// If you want to handle unsupported methods, you can use the following:
export function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      Allow: 'POST',
    },
  });
}
