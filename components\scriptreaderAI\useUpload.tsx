"use client";

import { getStorage, ref, uploadBytesResumable, getDownloadURL } from "firebase/storage";
import { useState, useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { doc, setDoc } from "firebase/firestore";
import { db } from "components/firebase";
import { useSession } from "next-auth/react";

export enum StatusText {
  UPLOADING = "Uploading file...",
  UPLOADED = "File uploaded successfully...",
  PROCESSING = "Processing file...",
  UPLOADING_ELEVENLABS = "Uploading to ElevenLabs...",
  INDEXING_RAG = "Indexing document for AI rehearsals...",
  COMPLETED = "Upload and processing complete.",
  ERROR = "An error occurred.",
  GENERATING = "GENERATING",
  SAVING = "SAVING",
  IDLE = "IDLE",
  AUTH_ERROR = "Authentication error. Please sign in again."
}

export type Status = StatusText;

const SUPPORTED_IMAGE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp",
];

const SUPPORTED_DOCUMENT_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "text/plain",
  "application/rtf",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "text/csv",
];

const MAX_IMAGE_SIZE = 4 * 1024 * 1024; // 4MB
const MAX_DOC_SIZE = 50 * 1024 * 1024; // 50MB

interface UploadHookResult {
  progress: number | null;
  status: Status | null;
  handleUpload: (
    file: File,
    category: string | null,
    userId: string,
    docId: string
  ) => Promise<void>;
  error: string | null;
}

function useUpload(): UploadHookResult {
  const [progress, setProgress] = useState<number | null>(null);
  const [status, setStatus] = useState<Status | null>(null);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { data: session } = useSession();

  const isImageFile = (file: File): boolean => SUPPORTED_IMAGE_TYPES.includes(file.type);
  const isDocumentFile = (file: File): boolean => SUPPORTED_DOCUMENT_TYPES.includes(file.type);

  const validateFileSize = (file: File): boolean => {
    return isImageFile(file) ? file.size <= MAX_IMAGE_SIZE : file.size <= MAX_DOC_SIZE;
  };

  const checkImageDimensions = (file: File): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!isImageFile(file)) {
        resolve(true);
        return;
      }
      const img = new Image();
      const objectUrl = URL.createObjectURL(file);
      img.onload = () => {
        URL.revokeObjectURL(objectUrl);
        const pixels = img.width * img.height;
        const MAX_IMAGE_PIXELS = 33177600;
        resolve(pixels <= MAX_IMAGE_PIXELS || (setError(`Image too large: ${pixels} pixels`), false));
      };
      img.onerror = () => {
        URL.revokeObjectURL(objectUrl);
        setError("Failed to read image dimensions");
        resolve(false);
      };
      img.src = objectUrl;
    });
  };

  const handleUpload = useCallback(
    async (
      file: File,
      category: string | null,
      userId: string,
      docId: string
    ) => {
      setError(null);

      if (!session?.user?.email) {
        setError("Authentication required. Please sign in again.");
        setStatus(StatusText.AUTH_ERROR);
        return;
      }

      if (!file || !userId || !docId) {
        setError("No file selected, user not authenticated, or document ID missing.");
        return;
      }

      if (!isImageFile(file) && !isDocumentFile(file)) {
        setError(`Unsupported file type: ${file.type}`);
        setStatus(StatusText.ERROR);
        return;
      }

      if (!validateFileSize(file)) {
        setError(`File size exceeds the ${isImageFile(file) ? "4MB" : "50MB"} limit`);
        setStatus(StatusText.ERROR);
        return;
      }

      if (!(await checkImageDimensions(file))) {
        setStatus(StatusText.ERROR);
        return;
      }

      setProgress(0);
      setStatus(StatusText.UPLOADING);

      try {
        const resolvedCategory = category || "SceneMate";
        const storage = getStorage();
        const storageRef = ref(storage, `uploads/${userId}/${docId}`);
        const uploadTask = uploadBytesResumable(storageRef, file);

        uploadTask.on(
          "state_changed",
          (snapshot) => {
            const percent = Math.round((snapshot.bytesTransferred / snapshot.totalBytes) * 100);
            setProgress(percent);
            console.log(`Upload progress: ${percent}%`);
          },
          (error) => {
            setError(`Error uploading file: ${error.message}`);
            setStatus(StatusText.ERROR);
            setProgress(null);
          },
          async () => {
            setStatus(StatusText.UPLOADED);
            const downloadUrl = await getDownloadURL(uploadTask.snapshot.ref);

            await setDoc(doc(db, "users", userId, "files", docId), {
              name: file.name,
              size: file.size,
              type: file.type,
              category: resolvedCategory,
              namespace: docId,
              downloadUrl: downloadUrl,
              ref: uploadTask.snapshot.ref.fullPath,
              createdAt: new Date(),
              isImage: isImageFile(file),
              elevenlabs_upload_requested: true, // Always true
            });

            setStatus(StatusText.PROCESSING);

            const response = await fetch("/api/processScriptfile", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                docId: docId,
                userId,
                category: resolvedCategory,
                fileName: file.name,
                fileType: file.type,
                fileUrl: downloadUrl,
                isImage: isImageFile(file),
                uploadToElevenLabs: true, // Always true
              }),
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error || `Server error: ${response.status}`);
            }

            const responseData = await response.json();
            if (responseData.elevenLabs) {
              if (responseData.elevenLabs.error) {
                console.warn("ElevenLabs upload warning:", responseData.elevenLabs.error);
              } else {
                console.log("Successfully uploaded to ElevenLabs Knowledge Base:", responseData.elevenLabs);
              }
            }

            setStatus(StatusText.COMPLETED);
          }
        );
      } catch (error: any) {
        setError(error.message || "Error during file upload or processing");
        setStatus(StatusText.ERROR);
        setProgress(null);
      }
    },
    [router, session]
  );

  return { progress, status, handleUpload, error };
}

export default useUpload;