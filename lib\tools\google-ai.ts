/**
 * Google AI Integration for CastMate
 * 
 * This module provides integration with Google's Generative AI (Gemini) models
 * for processing content in the CastMate application.
 */

import { GoogleGenerativeAI } from '@google/generative-ai';

export interface GoogleAIOptions {
  prompt: string;
  model?: string;
  modelOptions?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    topK?: number;
  };
}

/**
 * Process content using Google AI (Gemini)
 * @param options - Processing options including prompt, model, and model options
 * @returns Processed content from Google AI
 */
export async function processWithGoogleAI(options: GoogleAIOptions): Promise<string> {
  const {
    prompt,
    model = "gemini-2.5-pro-preview-05-06",
    modelOptions = {}
  } = options;

  const {
    temperature = 0.2,
    maxTokens = 7000,
    topP = 1.0,
    topK = 40
  } = modelOptions;

  try {
    // Get API key from environment variables
    const apiKey = process.env.GOOGLE_AI_API_KEY || process.env.GEMINI_API_KEY;
    
    if (!apiKey) {
      throw new Error('Google AI API key not found. Please set GOOGLE_AI_API_KEY or GEMINI_API_KEY environment variable.');
    }

    // Initialize Google AI client
    const genAI = new GoogleGenerativeAI(apiKey);
    
    // Get the model
    const geminiModel = genAI.getGenerativeModel({ 
      model: model,
      generationConfig: {
        temperature: temperature,
        maxOutputTokens: maxTokens,
        topP: topP,
        topK: topK,
      },
    });

    // Generate content
    const result = await geminiModel.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    if (!text) {
      throw new Error('No content received from Google AI');
    }

    return text;

  } catch (error) {
    console.error('Google AI processing error:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        throw new Error(`Google AI API key error: ${error.message}`);
      } else if (error.message.includes('quota')) {
        throw new Error(`Google AI quota exceeded: ${error.message}`);
      } else if (error.message.includes('model')) {
        throw new Error(`Google AI model error: ${error.message}. Available models: ${getGoogleAIModels().join(', ')}`);
      } else {
        throw new Error(`Google AI error: ${error.message}`);
      }
    }
    
    throw new Error(`Google AI processing failed: ${String(error)}`);
  }
}

/**
 * Get available Google AI models
 * @returns Array of available model names
 */
export function getGoogleAIModels(): string[] {
  return [
    'gemini-2.5-pro-preview-05-06',
    'gemini-2.0-flash-exp',
    'gemini-1.5-pro',
    'gemini-1.5-pro-002',
    'gemini-1.5-flash',
    'gemini-1.5-flash-002',
    'gemini-1.0-pro',
    'gemini-pro',
    'gemini-pro-vision'
  ];
}

/**
 * Check if a model is available
 * @param modelName - The model name to check
 * @returns True if the model is available
 */
export function isGoogleAIModelAvailable(modelName: string): boolean {
  return getGoogleAIModels().includes(modelName);
}

/**
 * Get the default Google AI model
 * @returns The default model name
 */
export function getDefaultGoogleAIModel(): string {
  return 'gemini-2.5-pro-preview-05-06';
}

/**
 * Validate Google AI configuration
 * @returns Object with validation results
 */
export function validateGoogleAIConfig(): {
  isValid: boolean;
  hasApiKey: boolean;
  apiKeySource?: string;
  error?: string;
} {
  try {
    const apiKey = process.env.GOOGLE_AI_API_KEY || process.env.GEMINI_API_KEY;
    const hasApiKey = !!apiKey;
    
    let apiKeySource: string | undefined;
    if (process.env.GOOGLE_AI_API_KEY) {
      apiKeySource = 'GOOGLE_AI_API_KEY';
    } else if (process.env.GEMINI_API_KEY) {
      apiKeySource = 'GEMINI_API_KEY';
    }

    if (!hasApiKey) {
      return {
        isValid: false,
        hasApiKey: false,
        error: 'No Google AI API key found. Please set GOOGLE_AI_API_KEY or GEMINI_API_KEY environment variable.'
      };
    }

    return {
      isValid: true,
      hasApiKey: true,
      apiKeySource
    };

  } catch (error) {
    return {
      isValid: false,
      hasApiKey: false,
      error: `Configuration validation error: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * Test Google AI connection
 * @returns Promise that resolves to connection test results
 */
export async function testGoogleAIConnection(): Promise<{
  success: boolean;
  model: string;
  responseTime?: number;
  error?: string;
}> {
  const startTime = Date.now();
  
  try {
    const testPrompt = "Hello! Please respond with 'Google AI connection successful' to confirm the connection is working.";
    
    const response = await processWithGoogleAI({
      prompt: testPrompt,
      model: getDefaultGoogleAIModel(),
      modelOptions: {
        temperature: 0.1,
        maxTokens: 50
      }
    });

    const responseTime = Date.now() - startTime;

    return {
      success: true,
      model: getDefaultGoogleAIModel(),
      responseTime
    };

  } catch (error) {
    return {
      success: false,
      model: getDefaultGoogleAIModel(),
      error: error instanceof Error ? error.message : String(error)
    };
  }
}
