/**
 * LLM Tool for processing content with various language models
 * 
 * This tool provides a unified interface for processing content with different LLM providers
 * including Groq and OpenAI.
 */

import { createGroqClient } from '../llms/groq';
import { Groq } from 'groq-sdk';

export type LlmProvider = 'groq' | 'openai';

export interface LlmProcessingOptions {
  prompt: string;
  context?: string;
  model?: string;
  provider?: LlmProvider;
  modelOptions?: {
    temperature?: number;
    maxTokens?: number;
    topP?: number;
  };
}

export interface LlmResponse {
  content: string;
  model: string;
  provider: LlmProvider;
  usage?: {
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
  };
}

export class LlmTool {
  private groqClient: Groq | null = null;

  constructor() {
    // Initialize with a default user email for system operations
    try {
      this.groqClient = createGroqClient({ userEmail: process.env.SYS_ADMIN || '<EMAIL>' });
    } catch (error) {
      console.warn('Failed to initialize Groq client:', error);
    }
  }

  /**
   * Process content with the specified LLM provider
   * @param options - Processing options including prompt, context, and model settings
   * @returns Processed content from the LLM
   */
  async processContent(options: LlmProcessingOptions): Promise<string> {
    const {
      prompt,
      context = '',
      model = 'llama-3.3-70b-versatile',
      provider = 'groq',
      modelOptions = {}
    } = options;

    const {
      temperature = 0.2,
      maxTokens = 7000,
      topP = 1.0
    } = modelOptions;

    try {
      switch (provider) {
        case 'groq':
          return await this.processWithGroq({
            prompt,
            context,
            model,
            temperature,
            maxTokens,
            topP
          });
        
        case 'openai':
          // For now, fallback to Groq if OpenAI is requested
          console.warn('OpenAI provider not implemented, falling back to Groq');
          return await this.processWithGroq({
            prompt,
            context,
            model: 'llama-3.3-70b-versatile',
            temperature,
            maxTokens,
            topP
          });
        
        default:
          throw new Error(`Unsupported LLM provider: ${provider}`);
      }
    } catch (error) {
      console.error('Error processing content with LLM:', error);
      throw new Error(`LLM processing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Process content using Groq
   */
  private async processWithGroq(options: {
    prompt: string;
    context: string;
    model: string;
    temperature: number;
    maxTokens: number;
    topP: number;
  }): Promise<string> {
    if (!this.groqClient) {
      throw new Error('Groq client not initialized');
    }

    const { prompt, context, model, temperature, maxTokens, topP } = options;

    // Combine context and prompt
    const messages: Array<{ role: 'system' | 'user'; content: string }> = [];
    
    if (context) {
      messages.push({
        role: 'system',
        content: context
      });
    }
    
    messages.push({
      role: 'user',
      content: prompt
    });

    try {
      const completion = await this.groqClient.chat.completions.create({
        messages,
        model,
        temperature,
        max_tokens: maxTokens,
        top_p: topP,
        stream: false
      });

      const content = completion.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error('No content received from Groq API');
      }

      return content;
    } catch (error) {
      console.error('Groq API error:', error);
      throw new Error(`Groq API error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get available models for a provider
   */
  getAvailableModels(provider: LlmProvider): string[] {
    switch (provider) {
      case 'groq':
        return [
          'llama-3.3-70b-versatile',
          'llama-3.1-70b-versatile',
          'llama-3.1-8b-instant',
          'mixtral-8x7b-32768',
          'gemma2-9b-it'
        ];
      
      case 'openai':
        return [
          'gpt-4',
          'gpt-4-turbo',
          'gpt-3.5-turbo'
        ];
      
      default:
        return [];
    }
  }

  /**
   * Check if a provider is available
   */
  isProviderAvailable(provider: LlmProvider): boolean {
    switch (provider) {
      case 'groq':
        return this.groqClient !== null;
      
      case 'openai':
        return false; // Not implemented yet
      
      default:
        return false;
    }
  }
}

// Export a singleton instance
export const llmTool = new LlmTool();
