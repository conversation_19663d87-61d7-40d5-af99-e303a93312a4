import React from 'react';
import { motion } from 'framer-motion';
import { Mi<PERSON>, Loader, MicOff, Volume2, VolumeX, Info } from 'lucide-react';
import { useSession } from 'next-auth/react';

interface RehearsalsProps {
  apiConfigStatus: 'unchecked' | 'valid' | 'invalid' | 'connecting';
  detailedErrorInfo: string | null;
  isListening: boolean;
  voiceStatus: string;
  isMuted: boolean;
  isSpeaking: boolean;
  hasPermission: boolean;
  voiceErrorMessage: string;
  toggleMute: () => Promise<void>;
  handleEndConversation: () => Promise<void>;
  handleStartConversation: () => Promise<void>;
  setVoiceErrorMessage: (message: string) => void;
}

const Rehearsals: React.FC<RehearsalsProps> = ({
  apiConfigStatus,
  detailedErrorInfo,
  isListening,
  voiceStatus,
  isMuted,
  isSpeaking,
  hasPermission,
  voiceErrorMessage,
  toggleMute,
  handleEndConversation,
  handleStartConversation,
  setVoiceErrorMessage
}) => {
  const { data: session } = useSession();

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-black/20 backdrop-blur-sm rounded-lg p-3 sm:p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
      >
        <h3 className="text-base sm:text-lg font-semibold text-white mb-4">Rehearsal Mode</h3>
        
        {/* ElevenLabs Configuration Status */}
        {apiConfigStatus === 'invalid' && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
            <h4 className="text-red-400 font-medium mb-1">ElevenLabs API Configuration Issue</h4>
            <p className="text-red-300 text-xs">Voice features are unavailable due to API configuration issues.</p>
            
            {detailedErrorInfo && (
              <div className="mt-2 p-2 bg-black/30 rounded border border-red-500/10 text-xs text-red-300 font-mono overflow-x-auto">
                {detailedErrorInfo}
              </div>
            )}
            
            <div className="mt-3 text-xs text-gray-400">
              <p>Recommended troubleshooting steps:</p>
              <ol className="list-decimal ml-4 mt-1 space-y-1">
                <li>Verify ElevenLabs API key is set in environment variables</li>
                <li>Ensure the Agent ID is correct and active</li>
                <li>Check network connectivity to ElevenLabs services</li>
                <li>Verify account subscription status and API limits</li>
              </ol>
            </div>
          </div>
        )}
        
        <div className="flex flex-col items-center justify-center space-y-4 py-6">
          <div className="relative">
            <div className={`w-16 h-16 flex items-center justify-center rounded-full ${
              isListening ? 'bg-purple-600' : 'bg-purple-600/20'
            } border border-purple-500/30`}>
              <Mic className={`w-8 h-8 ${isListening ? 'text-white' : 'text-purple-400'}`} />
            </div>
            {isListening && (
              <div className="absolute inset-0 w-16 h-16 rounded-full bg-purple-500 opacity-30 animate-ping" />
            )}
          </div>
          <p className="text-gray-300 text-center max-w-md">
            Practice your lines and receive feedback on your delivery and timing. Start rehearsing by selecting a scene or character.
          </p>
          
          {/* Voice connection status */}
          <div className="text-sm text-center">
            <span className={`inline-flex items-center px-2 py-1 rounded ${
              voiceStatus === "connected" 
                ? "bg-green-500/20 text-green-400" 
                : voiceStatus === "connecting" 
                  ? "bg-yellow-500/20 text-yellow-400"
                  : "bg-red-500/20 text-red-400"
            }`}>
              <span className={`w-2 h-2 rounded-full mr-1.5 ${
                voiceStatus === "connected" 
                  ? "bg-green-400" 
                  : voiceStatus === "connecting" 
                    ? "bg-yellow-400"
                    : "bg-red-400"
              }`}></span>
              {voiceStatus === "connected" 
                ? "Connected" 
                : voiceStatus === "connecting" 
                  ? "Connecting..."
                  : "Disconnected"}
            </span>
          </div>
          
          {/* Voice controls */}
          <div className="flex items-center justify-center space-x-4 mt-4">
            <button
              onClick={toggleMute}
              disabled={voiceStatus !== "connected"}
              className={`p-3 rounded-full shadow-lg ${
                voiceStatus === "connected" 
                  ? (isMuted ? "bg-gray-700 hover:bg-gray-600" : "bg-purple-600 hover:bg-purple-700") 
                  : "bg-gray-700 opacity-50 cursor-not-allowed"
              } transition-colors`}
              title={isMuted ? "Unmute" : "Mute"}
            >
              {isMuted ? (
                <VolumeX className="h-5 w-5 text-white" />
              ) : (
                <Volume2 className="h-5 w-5 text-white" />
              )}
            </button>

            {voiceStatus === "connected" ? (
              <button
                onClick={handleEndConversation}
                className="flex items-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg shadow-lg shadow-red-600/20 transition-colors"
              >
                <MicOff className="h-5 w-5" />
                Stop Rehearsing
              </button>
            ) : (
              <button
                onClick={handleStartConversation}
                className={`flex items-center gap-2 px-6 py-3 text-white rounded-lg shadow-lg transition-colors ${
                  !hasPermission || apiConfigStatus === 'invalid' || !session?.user?.email || apiConfigStatus === 'connecting'
                    ? "bg-gray-700 opacity-50 cursor-not-allowed"
                    : "bg-purple-600 hover:bg-purple-700 shadow-purple-600/20"
                }`}
                disabled={!hasPermission || apiConfigStatus === 'invalid' || !session?.user?.email || apiConfigStatus === 'connecting'}
              >
                {apiConfigStatus === 'connecting' ? (
                  <Loader className="h-5 w-5 animate-spin" />
                ) : (
                  <Mic className="h-5 w-5" />
                )}
                Start Rehearsing
              </button>
            )}
          </div>

          {/* Voice status indicators */}
          {voiceStatus === "connected" && (
            <div className="text-sm mt-2">
              {isSpeaking ? (
                <span className="text-green-400">AI Assistant is speaking...</span>
              ) : isListening ? (
                <span className="text-purple-400">Listening to your lines...</span>
              ) : null}
            </div>
          )}

          {voiceErrorMessage && (
            <div className="text-red-400 text-sm bg-red-500/10 p-2 rounded-lg mt-2">
              {voiceErrorMessage}
              <button
                onClick={() => setVoiceErrorMessage("")}
                className="ml-2 text-purple-400 hover:text-purple-300 underline"
              >
                Dismiss
              </button>
            </div>
          )}

          {!hasPermission && (
            <div className="text-yellow-400 text-sm bg-yellow-500/10 p-2 rounded-lg mt-2">
              Please allow microphone access to use rehearsal mode
            </div>
          )}
        </div>
        
        <div className="mt-6 border-t border-white/10 pt-4">
          <h4 className="text-sm font-medium text-purple-400 mb-2">Recent Rehearsals</h4>
          <div className="text-xs text-gray-400">
            No recent rehearsals found. Start rehearsing to track your progress.
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Rehearsals;